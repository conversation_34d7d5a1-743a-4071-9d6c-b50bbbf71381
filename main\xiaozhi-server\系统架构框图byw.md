# xiaozhi-server 系统架构框图

## 整体系统架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          xiaozhi-esp32-server 生态系统                           │
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐              │
│  │  manager-web    │    │  manager-api    │    │ manager-mobile  │              │
│  │   (Vue.js)      │◄──►│ (Spring Boot)   │◄──►│   (uni-app)     │              │
│  │   端口: 8001    │    │   端口: 8002    │    │   跨平台应用     │              │
│  │   Web管理界面   │    │   RESTful API   │    │   移动端管理     │              │
│  └─────────────────┘    └─────┬───────────┘    └─────────────────┘              │
│           │                   │ HTTP/REST API                                   │
│           │                   │                                                 │
│           │                   ▼                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                    xiaozhi-server (Python核心服务)                          ││
│  │                                                                             ││
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            ││
│  │  │  WebSocket服务   │  │   HTTP服务      │  │   配置管理       │            ││
│  │  │   端口: 8000    │  │   端口: 8003    │  │  动态更新配置     │            ││
│  │  │  ESP32通信      │  │  OTA固件更新    │  │  组件热替换       │            ││
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────┬───────────────────────────────────────┘
                                          │ WebSocket 实时通信
                                          ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              ESP32 智能硬件设备                                   │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   麦克风     │    │   扬声器     │    │  IoT控制器   │    │  传感器模块  │     │
│  │  音频采集    │    │  音频播放    │    │  设备控制    │    │  环境监测    │     │
│  │  16kHz采样   │    │  实时播放    │    │  智能家居    │    │  数据上报    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## xiaozhi-server 内部架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          xiaozhi-server 核心架构                                │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            应用程序入口 (app.py)                            ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐       ││
│  │  │ 环境检查     │  │ 配置加载     │  │ 服务启动     │  │ 优雅关闭     │       ││
│  │  │ FFmpeg等    │  │ config.yaml │  │ 并发启动     │  │ 信号处理     │       ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘       ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                         WebSocket服务器 (websocket_server.py)               ││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                        AI组件管理与初始化                               │││
│  │  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐          │││
│  │  │  │   VAD   │ │   ASR   │ │   LLM   │ │ Memory  │ │ Intent  │          │││
│  │  │  │ 语音活动 │ │ 语音识别 │ │ 大模型  │ │ 记忆管理 │ │ 意图识别 │          │││
│  │  │  │ 检测模块 │ │ 转换模块 │ │ 对话生成 │ │ 上下文  │ │ 函数调用 │          │││
│  │  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘          │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  │                                        │                                   ││
│  │                                        ▼                                   ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                          连接管理 & 会话隔离                            │││
│  │  │  ┌────────────────────┐  ┌────────────────────┐  ┌──────────────────────┤││
│  │  │  │ ConnectionHandler  │  │ ConnectionHandler  │  │ ConnectionHandler... │││
│  │  │  │    (ESP32-A)       │  │    (ESP32-B)       │  │    (ESP32-N)         │││
│  │  │  │ ┌────────────────┐ │  │ ┌────────────────┐ │  │ ┌──────────────────┐ │││
│  │  │  │ │ 独立会话状态   │ │  │ │ 独立会话状态   │ │  │ │ 独立会话状态     │ │││
│  │  │  │ │ 音频缓冲管理   │ │  │ │ 音频缓冲管理   │ │  │ │ 音频缓冲管理     │ │││
│  │  │  │ │ AI组件引用     │ │  │ │ AI组件引用     │ │  │ │ AI组件引用       │ │││
│  │  │  │ └────────────────┘ │  │ └────────────────┘ │  │ └──────────────────┘ │││
│  │  │  └────────────────────┘  └────────────────────┘  └──────────────────────┤││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                        动态配置更新系统                                      │
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │  config_lock (异步锁) ──► 配置热更新 ──► 组件重新初始化                  │││
│  │  │  ┌─────────────┐       ┌─────────────┐       ┌─────────────┐           │││
│  │  │  │ manager-api │ ────► │ 配置检查     │ ────► │ 模块热替换   │           │││
│  │  │  │ 拉取新配置   │       │ VAD/ASR等   │       │ 无需重启     │           │││
│  │  │  └─────────────┘       └─────────────┘       └─────────────┘           │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 连接处理器详细架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        ConnectionHandler 内部架构                               │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                              会话管理层                                      ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           ││
│  │  │ session_id  │ │ device_id   │ │ client_ip   │ │ 认证信息     │           ││
│  │  │ 会话唯一标识 │ │ 设备标识    │ │ 客户端IP    │ │ auth_key    │           ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘           ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            状态管理与控制层                                  ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           ││
│  │  │ client_abort│ │ is_speaking │ │ listen_mode │ │ voice_stop  │           ││
│  │  │ 客户端中断   │ │ 正在说话    │ │ 监听模式    │ │ 语音结束    │           ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘           ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            音频处理缓冲层                                    ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           ││
│  │  │ audio_buffer│ │ audio_queue │ │ voice_window│ │ asr_audio   │           ││
│  │  │ 音频缓冲区   │ │ 音频队列    │ │ 语音窗口    │ │ ASR音频    │           ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘           ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            消息处理路由层                                    ││
│  │                                                                             ││
│  │  WebSocket消息 ──► 类型检测 ──► 专用处理器 ──► 响应生成                      ││
│  │                                                                             ││
│  │  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         ││
│  │  │   消息类型       │    │   处理器模块     │    │   响应动作       │         ││
│  │  ├─────────────────┤    ├─────────────────┤    ├─────────────────┤         ││
│  │  │ 二进制(音频)     │──► │receiveAudio     │──► │ VAD+ASR处理     │         ││
│  │  │ hello消息       │──► │helloHandle      │──► │ 设备认证绑定     │         ││
│  │  │ listen控制      │──► │textHandle       │──► │ 监听模式切换     │         ││
│  │  │ abort中断       │──► │abortHandle      │──► │ TTS播放中断     │         ││
│  │  │ iot设备         │──► │iotHandle        │──► │ 设备状态控制     │         ││
│  │  │ mcp协议         │──► │mcpHandle        │──► │ MCP消息处理     │         ││
│  │  │ server命令      │──► │textHandle       │──► │ 配置动态更新     │         ││
│  │  └─────────────────┘    └─────────────────┘    └─────────────────┘         ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 语音处理流水线架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            语音处理完整流水线                                    │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                           音频输入处理阶段                                   ││
│  │                                                                             ││
│  │  ESP32设备 ──► WebSocket ──► ConnectionHandler ──► receiveAudioHandle       ││
│  │     │              │               │                        │               ││
│  │     ▼              ▼               ▼                        ▼               ││
│  │  ┌─────────┐  ┌─────────┐  ┌─────────────┐  ┌─────────────────────────┐    ││
│  │  │ 麦克风   │  │ Opus    │  │ 音频缓冲     │  │        VAD处理          │    ││
│  │  │ 16kHz   │  │ 压缩传输 │  │ 队列管理     │  │    ┌─────────────────┐  │    ││
│  │  │ 采样     │  │ 实时流  │  │ 缓冲区控制   │  │    │   SileroVAD    │  │    ││
│  │  │         │  │         │  │ 内存管理     │  │    │   语音活动检测   │  │    ││
│  │  └─────────┘  └─────────┘  └─────────────┘  │    │   静音切割      │  │    ││
│  │                                             │    │   语音窗口检测   │  │    ││
│  │                                             │    └─────────────────┘  │    ││
│  │                                             └─────────────────────────────┘    ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                          语音识别与理解阶段                                   ││
│  │                                                                             ││
│  │  语音片段 ──► ASR并行处理 ──► 文本结果 ──► Intent分析 ──► LLM处理             ││
│  │       │            │            │            │            │                ││
│  │       ▼            ▼            ▼            ▼            ▼                ││
│  │  ┌─────────┐  ┌──────────────┐ ┌──────────┐ ┌──────────┐ ┌─────────────┐  ││
│  │  │ Opus    │  │   ASR处理    │ │ 文本输出  │ │ 意图识别 │ │   LLM对话   │  ││
│  │  │ 解码    │  │ ┌──────────┐ │ │ 文本清理  │ │ 函数调用 │ │   上下文   │  ││
│  │  │ PCM     │  │ │本地FunASR │ │ │ 长度检查  │ │ 工具匹配 │ │   记忆管理  │  ││
│  │  │ 转换    │  │ │云端ASR   │ │ │          │ │          │ │   响应生成  │  ││
│  │  │         │  │ │声纹识别  │ │ │          │ │          │ │            │  ││
│  │  └─────────┘  │ └──────────┘ │ └──────────┘ └──────────┘ └─────────────┘  ││
│  │               │ 并发执行15s   │                                            ││
│  │               └──────────────┘                                            ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                          工具调用与功能执行阶段                               ││
│  │                                                                             ││
│  │  LLM意图 ──► 插件匹配 ──► 函数执行 ──► 结果返回 ──► 响应合成                 ││
│  │      │           │           │           │           │                     ││
│  │      ▼           ▼           ▼           ▼           ▼                     ││
│  │  ┌─────────┐ ┌─────────┐ ┌─────────────┐ ┌─────────┐ ┌─────────────┐      ││
│  │  │ Function│ │ 插件系统 │ │   工具调用   │ │ API调用 │ │  最终回复   │      ││
│  │  │ Call    │ │ 自动加载 │ │ ┌─────────┐ │ │ IoT控制 │ │  文本生成   │      ││
│  │  │ 检测    │ │ Schema  │ │ │天气查询 │ │ │ 设备操作 │ │  上下文整合 │      ││
│  │  │         │ │ 匹配    │ │ │智能家居 │ │ │ MCP协议 │ │            │      ││
│  │  │         │ │         │ │ │音乐播放 │ │ │         │ │            │      ││
│  │  └─────────┘ └─────────┘ │ └─────────┘ │ └─────────┘ └─────────────┘      ││
│  │                         │   并发执行   │                                  ││
│  │                         └─────────────┘                                  ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            语音合成与输出阶段                                ││
│  │                                                                             ││
│  │  文本回复 ──► TTS合成 ──► 音频流 ──► WebSocket ──► ESP32播放                  ││
│  │       │          │          │          │          │                       ││
│  │       ▼          ▼          ▼          ▼          ▼                       ││
│  │  ┌─────────┐ ┌─────────────┐ ┌──────────┐ ┌─────────┐ ┌─────────┐         ││
│  │  │ 文本处理 │ │   TTS引擎   │ │ 音频编码  │ │ 实时传输 │ │ 设备播放│         ││
│  │  │ 长度分割 │ │ ┌─────────┐ │ │ 格式转换  │ │ 二进制帧 │ │ 扬声器  │         ││
│  │  │ 标点处理 │ │ │Edge TTS │ │ │ 压缩优化  │ │ 流式发送 │ │ 音量控制│         ││
│  │  │ 特殊字符 │ │ │云端TTS  │ │ │ 质量控制  │ │         │ │        │         ││
│  │  │ 过滤    │ │ │本地TTS  │ │ │          │ │         │ │        │         ││
│  │  └─────────┘ │ └─────────┘ │ └──────────┘ └─────────┘ └─────────┘         ││
│  │              │ 流式合成     │                                             ││
│  │              └─────────────┘                                             ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 提供商系统架构

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           AI服务提供商系统架构                                    │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            Provider抽象层                                   ││
│  │                                                                             ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           ││
│  │  │ ASRProvider │ │ LLMProvider │ │ TTSProvider │ │ VADProvider │           ││
│  │  │    Base     │ │    Base     │ │    Base     │ │    Base     │           ││
│  │  │ 抽象基类     │ │ 抽象基类     │ │ 抽象基类     │ │ 抽象基类     │           ││
│  │  │ 统一接口     │ │ 统一接口     │ │ 统一接口     │ │ 统一接口     │           ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘           ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                           ││
│  │  │MemoryBase  │ │ IntentBase  │ │ VLLMBase    │                           ││
│  │  │ 记忆管理     │ │ 意图识别     │ │ 视觉模型     │                           ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘                           ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                          具体实现层 (多种选择)                                ││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                        ASR语音识别实现                                  │││
│  │  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │││
│  │  │ │ FunASR本地  │ │ DoubaoASR   │ │ BaiduASR    │ │ OpenAI ASR  │       │││
│  │  │ │ 离线识别     │ │ 豆包云识别   │ │ 百度云识别   │ │ Whisper    │       │││
│  │  │ │ GPU加速     │ │ 流式处理     │ │ 高精度      │ │ 多语言      │       │││
│  │  │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                        LLM大语言模型实现                                │││
│  │  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │││
│  │  │ │ OpenAI GPT  │ │ DoubaoLLM   │ │ ChatGLM     │ │ Ollama本地  │       │││
│  │  │ │ GPT-4等     │ │ 豆包大模型   │ │ 智谱AI      │ │ 私有部署    │       │││
│  │  │ │ 云端调用     │ │ 函数调用     │ │ 免费额度    │ │ 离线使用    │       │││
│  │  │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                        TTS语音合成实现                                  │││
│  │  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │││
│  │  │ │ Edge TTS    │ │ 阿里云TTS   │ │ 火山TTS     │ │ 本地TTS     │       │││
│  │  │ │ 免费使用     │ │ 高质量合成   │ │ 流式输出    │ │ FishSpeech  │       │││
│  │  │ │ 多语言音色   │ │ 商用授权    │ │ 低延迟      │ │ GPU合成     │       │││
│  │  │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            工厂模式加载                                      ││
│  │                                                                             ││
│  │  config.yaml配置 ──► modules_initialize.py ──► 动态实例化 ──► 注册到系统     ││
│  │                                                                             ││
│  │  ┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐             ││
│  │  │ 配置文件     │    │   工厂函数       │    │   实例管理       │             ││
│  │  │ selected_   │──► │ create_instance │──► │ WebSocketServer │             ││
│  │  │ module     │    │ 动态导入类      │    │ 统一管理        │             ││
│  │  │ provider   │    │ 参数传递        │    │ 生命周期控制     │             ││
│  │  │ 选择       │    │ 错误处理        │    │ 热替换支持      │             ││
│  │  └─────────────┘    └─────────────────┘    └─────────────────┘             ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 配置管理流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                             配置管理完整流程                                      │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            配置源与加载                                      ││
│  │                                                                             ││
│  │  ┌─────────────┐            ┌─────────────┐            ┌─────────────┐      ││
│  │  │   本地配置   │            │  远程API配置 │            │   环境变量   │      ││
│  │  │             │            │             │            │             │      ││
│  │  │ config.yaml │     或     │ manager-api │    合并     │ 运行时参数   │      ││
│  │  │ 开发环境    │   ────►    │ MySQL存储   │   ────►    │ 覆盖配置    │      ││
│  │  │ 基础配置    │            │ 生产环境    │            │ 临时调整    │      ││
│  │  │ 默认设置    │            │ 统一管理    │            │ 调试模式    │      ││
│  │  └─────────────┘            └─────────────┘            └─────────────┘      ││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            配置解析与验证                                    ││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                         配置加载流程                                    │││
│  │  │                                                                         │││
│  │  │  1. 读取默认配置    ──► 2. 检查API配置    ──► 3. 合并配置               │││
│  │  │                                                                         │││
│  │  │  ┌─────────────┐       ┌─────────────┐       ┌─────────────┐           │││
│  │  │  │ default     │       │ check API   │       │ merge       │           │││
│  │  │  │ config.yaml │ ───►  │ manager-api │ ───►  │ configs     │           │││
│  │  │  │ 基线配置     │       │ URL检查     │       │ 优先级处理   │           │││
│  │  │  └─────────────┘       └─────────────┘       └─────────────┘           │││
│  │  │                                                      │                 │││
│  │  │                                                      ▼                 │││
│  │  │  ┌─────────────────────────────────────────────────────────────────────┤││
│  │  │  │                     配置验证与目录创建                              │││
│  │  │  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │││
│  │  │  │  │ 路径验证     │  │ 权限检查     │  │ 目录创建     │                │││
│  │  │  │  │ AI服务配置  │  │ 文件访问    │  │ 日志目录    │                │││
│  │  │  │  │ 密钥格式    │  │ 网络连接    │  │ 模型目录    │                │││
│  │  │  │  └─────────────┘  └─────────────┘  └─────────────┘                │││
│  │  │  └─────────────────────────────────────────────────────────────────────┤││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────────────────────┘│
│                                        │                                        │
│                                        ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐│
│  │                            动态配置更新机制                                  ││
│  │                                                                             ││
│  │  管理员操作 ──► Web界面修改 ──► API保存 ──► 通知更新 ──► 热替换组件            ││
│  │                                                                             ││
│  │  ┌─────────────────────────────────────────────────────────────────────────┐││
│  │  │                       配置更新完整链路                                  │││
│  │  │                                                                         │││
│  │  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                │││
│  │  │  │ 管理员      │    │ manager-web │    │ manager-api │                │││
│  │  │  │ 修改AI配置  │──► │ Vue前端界面 │──► │ 后端API     │                │││
│  │  │  │ 选择模型    │    │ 表单验证    │    │ 数据验证    │                │││
│  │  │  │ 更新密钥    │    │ 实时预览    │    │ MySQL存储   │                │││
│  │  │  └─────────────┘    └─────────────┘    └─────────────┘                │││
│  │  │                                                  │                     │││
│  │  │                                                  ▼                     │││
│  │  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                │││
│  │  │  │xiaozhi      │    │ 配置拉取     │    │ 组件重载     │                │││
│  │  │  │-server     │◄─── │ HTTP请求    │◄─── │ 异步锁保护   │                │││
│  │  │  │ 应用配置    │    │ JSON解析    │    │ 热替换处理   │                │││
│  │  │  │ 立即生效    │    │ 差异检测    │    │ 无缝切换    │                │││
│  │  │  └─────────────┘    └─────────────┘    └─────────────┘                │││
│  │  └─────────────────────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 系统特性总结

### 🚀 核心优势
- **异步高性能**: 基于asyncio的并发处理，支持多设备同时连接
- **插件化架构**: Provider模式支持多种AI服务的无缝切换
- **实时通信**: WebSocket协议保证低延迟的音频交互
- **动态配置**: 支持运行时配置更新，无需重启服务
- **多模态支持**: 音频、文本、视觉的统一处理框架

### 🔧 扩展能力
- **新AI服务**: 继承Base类即可接入新的AI提供商
- **功能插件**: 支持自定义插件开发和热加载
- **协议扩展**: 支持IoT、MCP等多种设备控制协议
- **多端管理**: Web、移动端的统一管理界面

### 🛡️ 稳定可靠
- **异常容错**: 各模块独立的异常处理和恢复机制
- **资源管理**: 智能的内存和连接池管理
- **监控日志**: 结构化日志和性能监控
- **优雅关闭**: 完善的服务关闭和资源清理

xiaozhi-server通过这套精心设计的架构，为ESP32等智能硬件设备提供了强大而灵活的AI语音交互能力，支持从个人DIY项目到企业级部署的各种应用场景。
