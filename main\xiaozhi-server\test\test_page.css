body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 10px 20px 10px 20px;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

.section {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.section h2 {
    margin-top: 0;
    color: #444;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
}

.section h2 .toggle-button {
    margin-left: auto;
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    height: 28px;
    line-height: 20px;
}

.device-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: 20px;
    padding: 0 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    height: 28px;
    line-height: 28px;
}

.device-info span {
    color: #666;
    font-size: 13px;
}

.device-info strong {
    color: #333;
    font-weight: 500;
}

.config-panel {
    display: none;
    transition: all 0.3s ease;
    margin-top: 5px;
    padding: 5px 0;
}

.config-panel.expanded {
    display: block;
}

.control-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

button {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: #4285f4;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #3367d6;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

#serverUrl,
#otaUrl {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.message-input {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#messageInput {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

#nfcCardId {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.conversation {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    background-color: white;
    flex: 1;
    margin-right: 10px;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    max-width: 80%;
}

.user {
    background-color: #e2f2ff;
    margin-left: auto;
    margin-right: 10px;
    text-align: right;
}

.server {
    background-color: #f0f0f0;
    margin-right: auto;
    margin-left: 10px;
}

.status {
    color: #666;
    font-style: italic;
    font-size: 14px;
    margin: 0;
    padding: 0;
}

.audio-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.audio-visualizer {
    height: 60px;
    width: 100%;
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #fafafa;
}

.record-button {
    background-color: #db4437;
}

.record-button:hover {
    background-color: #c53929;
}

.record-button.recording {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        background-color: #db4437;
    }

    50% {
        background-color: #ff6659;
    }

    100% {
        background-color: #db4437;
    }
}

#logContainer {
    margin-top: 0;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
    font-family: monospace;
    height: 300px;
    overflow-y: auto;
    flex: 1;
    margin-left: 10px;
}

.log-entry {
    margin: 5px 0;
    font-size: 12px;
}

.log-info {
    color: #333;
}

.log-error {
    color: #db4437;
}

.log-success {
    color: #0f9d58;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.script-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.script-loaded {
    background-color: #0f9d58;
}

.script-loading {
    background-color: #f4b400;
}

.script-error {
    background-color: #db4437;
}

.script-list {
    margin: 10px 0;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    font-family: monospace;
    font-size: 11px;
}

#scriptStatus.success {
    background-color: #e6f4ea;
    color: #0f9d58;
    border-left: 4px solid #0f9d58;
}

#scriptStatus.error {
    background-color: #fce8e6;
    color: #db4437;
    border-left: 4px solid #db4437;
}

#scriptStatus.warning {
    background-color: #fef7e0;
    color: #f4b400;
    border-left: 4px solid #f4b400;
}

/* 标签页样式 */
.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.tab {
    padding: 10px 20px;
    cursor: pointer;
    border: none;
    background: none;
    font-size: 16px;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.tab:hover {
    color: #4285f4;
}

.tab.active {
    color: #4285f4;
    font-weight: bold;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4285f4;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.flex-container {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.config-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    width: 100%;
}

.config-item label {
    width: 100px;
    text-align: right;
    margin-right: 10px;
    color: #666;
}

.config-item input {
    flex-grow: 1;
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.control-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.connection-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    width: 100%;
}

.connection-controls input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    min-width: 200px;
}

.connection-controls button {
    white-space: nowrap;
    padding: 8px 15px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: 20px;
    padding: 0 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    height: 28px;
    line-height: 28px;
}

.connection-status span {
    color: #666;
    font-size: 13px;
}

.connection-status .status {
    color: #333;
    font-weight: 500;
}